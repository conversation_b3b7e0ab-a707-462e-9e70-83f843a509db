<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道调拨与任务执行综合报告</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
    <style>
        :root {
            --primary: #3498db;
            --success: #27ae60;
            --danger: #e74c3c;
            --warning: #f39c12;
            --info: #9b59b6;
        }
        body { margin: 20px; font-family: "Microsoft YaHei", sans-serif; color: #333; background-color: #f8fafc; max-width: 100vw; overflow-x: hidden; }
        .container { max-width: 1200px; margin: 0 auto; width: 100%; box-sizing: border-box; overflow-x: hidden; }
        h1 { color: #2c3e50; border-bottom: 2px solid var(--primary); padding-bottom: 10px; text-align: center; }
        h2 { color: var(--primary); margin-top: 30px; text-align: left; border-left: 5px solid var(--primary); padding-left: 10px; }
        /* 优化年度数据对比标题左侧线条长度 */
        h2[data-short-line] {
            border-left: 5px solid var(--primary);
            height: auto;
            line-height: 1.2;
            display: inline-block;
            padding-left: 10px;
            margin-bottom: 0.5em;
        }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; box-shadow: 0 2px 5px rgba(0,0,0,0.05); background: #fff; max-width: 100%; }
        th, td { padding: 12px 15px; text-align: center; border-bottom: 1px solid #ddd; }
        th { background-color: var(--primary); color: white; font-weight: bold; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .trend-up { color: var(--success); font-weight: bold; }
        .trend-down { color: var(--danger); font-weight: bold; }
        .percentage { font-weight: bold; color: var(--primary); }
        .chart-container { display: flex; flex-wrap: wrap; margin: 30px 0; gap: 20px; }
        .chart-box { flex: 1; min-width: 300px; max-width: 1100px; padding: 18px; box-shadow: 0 2px 10px rgba(0,0,0,0.08); border-radius: 8px; background: #fff; margin: 0 auto; page-break-inside: avoid; }
        .chart-wrapper { position: relative; width: 100%; max-width: 1100px; height: 340px; min-height: 340px; margin: 0 auto; page-break-inside: avoid; }
        #chart_sales_compare, #chart_ratio_trend { width: 100%; max-width: 800px; height: 340px; min-height: 340px; margin: 0 auto 30px auto; }
        @media print {
            /* 页面基础设置 */
            @page {
                size: A4;
                margin: 15mm;
            }

            body, .container {
                background: #fff !important;
                color: #222 !important;
                max-width: none !important;
                width: 100% !important;
                font-size: 12px !important;
                line-height: 1.4 !important;
            }

            /* 图表容器优化 - 确保图表完整显示 */
            .chart-box, .chart-wrapper {
                max-width: 100% !important;
                width: 100% !important;
                height: 300px !important;
                min-height: 300px !important;
                margin: 10px 0 !important;
                padding: 10px !important;
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                box-sizing: border-box !important;
                overflow: visible !important;
            }

            /* 主要图表尺寸设置 */
            #mainChart, #taskTrendChart {
                width: 100% !important;
                height: 300px !important;
                min-height: 300px !important;
                max-width: 100% !important;
                page-break-inside: avoid !important;
            }

            /* ECharts图表容器 */
            #chart_sales_compare, #chart_ratio_trend {
                width: 100% !important;
                height: 300px !important;
                min-height: 300px !important;
                max-width: 100% !important;
                margin: 10px 0 !important;
                page-break-inside: avoid !important;
            }

            /* Chart.js图表容器 */
            #channel1SalesTransferChart, #channel2SalesTransferChart {
                width: 100% !important;
                height: 280px !important;
                max-width: 100% !important;
            }

            /* 图表容器布局 */
            .chart-container {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                margin: 15px 0 !important;
                display: block !important;
                width: 100% !important;
            }

            /* 两列布局优化 */
            .two-columns {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                display: flex !important;
                gap: 8px !important;
                margin: 15px 0 !important;
            }

            .two-columns .column:first-child {
                flex: 0 0 48% !important;
                min-width: 0 !important;
                page-break-inside: avoid !important;
            }

            .two-columns .column:last-child {
                flex: 0 0 52% !important;
                min-width: 0 !important;
                page-break-inside: avoid !important;
                margin-left: -5px !important;
            }

            .two-columns .column:first-child .chart-container {
                height: 350px !important;
                margin: 5px 0 10px 0 !important;
                display: block !important;
                padding: 8px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            .two-columns .column:last-child .chart-container {
                height: 350px !important;
                margin: 5px 0 10px 0 !important;
                display: block !important;
                padding: 6px !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }

            /* 两列布局中的列优化 */
            .two-columns .column {
                display: flex !important;
                flex-direction: column !important;
                width: 50% !important;
                box-sizing: border-box !important;
            }

            /* Chart.js图表容器内的描述文字 - PDF优化 */
            .two-columns .column:first-child .chart-container .chart-description {
                margin: 0 !important;
                padding: 5px !important;
                font-size: 8px !important;
                line-height: 1.4 !important;
                text-align: left !important;
                background-color: #f8f9fa !important;
                border: 1px solid #ddd !important;
                border-radius: 2px !important;
                width: calc(100% - 12px) !important;
                box-sizing: border-box !important;
            }

            .two-columns .column:last-child .chart-container .chart-description {
                margin: 0 !important;
                padding: 4px !important;
                font-size: 8px !important;
                line-height: 1.4 !important;
                text-align: left !important;
                background-color: #f8f9fa !important;
                border: 1px solid #ddd !important;
                border-radius: 2px !important;
                width: calc(100% - 10px) !important;
                box-sizing: border-box !important;
            }

            /* Chart.js图表本身的尺寸和位置优化 */
            .two-columns .chart-container .chart {
                height: 250px !important;
                width: 100% !important;
                max-width: 100% !important;
                margin: 0 auto 12px auto !important;
            }

            /* Canvas图表尺寸优化 */
            .two-columns .chart-container canvas {
                width: 100% !important;
                height: 250px !important;
                max-width: 100% !important;
                margin-bottom: 8px !important;
            }

            /* 文字段内的div元素优化 */
            .two-columns .chart-container .chart-description div {
                margin-bottom: 2px !important;
                text-align: left !important;
            }

            /* PDF打印时的特殊间距优化 - 防止文本框遮挡横坐标轴 */
            @media print {
                .two-columns .chart-container .chart {
                    margin-bottom: 15px !important;
                    padding-bottom: 10px !important;
                }

                .two-columns .chart-container canvas {
                    margin-bottom: 12px !important;
                }

                .chart-description {
                    margin-top: 8px !important;
                }
            }

            /* 确保图表说明文字中的span元素正常显示 */
            .two-columns .chart-container span {
                display: inline !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* 专门针对图表描述文字的样式 - PDF优化 */
            .chart-description {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                margin: 0 !important;
                padding: 5px !important;
                font-size: 8px !important;
                line-height: 1.4 !important;
                color: #333 !important;
                background-color: #f8f9fa !important;
                border: 1px solid #ddd !important;
                border-radius: 2px !important;
                text-align: left !important;
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                width: calc(100% - 12px) !important;
                box-sizing: border-box !important;
            }

            /* 文字段内的div元素样式 */
            .chart-description div {
                margin-bottom: 2px !important;
                text-align: left !important;
                line-height: 1.4 !important;
            }

            .chart-description span {
                display: inline !important;
                visibility: visible !important;
                opacity: 1 !important;
                color: inherit !important;
            }

            /* 进一步优化PDF中Canvas图表与文字段的间距 */
            .two-columns .chart-container canvas {
                margin-bottom: 0 !important;
            }

            /* 确保文字段紧贴图表底部 */
            .two-columns .chart-container {
                padding-bottom: 0 !important;
            }

            /* 统一行高和字体大小 - 紧凑布局优化 */
            .chart-description {
                font-size: 0.82em !important;
                line-height: 1.5 !important;
                margin: 0 !important;
                padding: 5px !important;
                width: calc(100% - 12px) !important;
            }

            /* 确保文字段与图表两端对齐 */
            .chart-description div {
                text-align: left !important;
                margin-bottom: 2px !important;
            }

            /* 标题和文本优化 */
            h1 {
                font-size: 18px !important;
                margin: 10px 0 !important;
                page-break-after: avoid !important;
            }

            h2 {
                font-size: 14px !important;
                margin: 15px 0 8px 0 !important;
                page-break-after: avoid !important;
                break-after: avoid !important;
            }

            h3 {
                font-size: 12px !important;
                margin: 10px 0 5px 0 !important;
                page-break-after: avoid !important;
                break-after: avoid !important;
            }

            /* 表格优化 */
            table {
                page-break-inside: avoid !important;
                break-inside: avoid !important;
                font-size: 10px !important;
                width: 100% !important;
            }

            th, td {
                padding: 4px 6px !important;
                font-size: 10px !important;
            }

            /* KPI卡片优化 */
            .kpi-cards {
                display: flex !important;
                flex-direction: row !important;
                justify-content: space-between !important;
                gap: 10px !important;
                page-break-inside: avoid !important;
                margin: 10px 0 !important;
            }

            .kpi-card {
                flex: 1 !important;
                min-width: 0 !important;
                max-width: 32% !important;
                padding: 8px !important;
                font-size: 10px !important;
                page-break-inside: avoid !important;
            }

            /* 分析文本区域 */
            .analysis-text, .data-section {
                page-break-inside: avoid !important;
                margin: 10px 0 !important;
                padding: 8px !important;
                font-size: 11px !important;
            }

            /* 强制分页控制 */
            .chart-container:not(.two-columns .chart-container) {
                page-break-after: auto !important;
            }

            /* 隐藏不必要的元素 */
            .chart-box {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
        }
        .data-section, .analysis-text { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; page-break-inside: avoid; break-inside: avoid; }
        .highlight { font-weight: bold; color: #2c3e50; }
        .circle-highlight { color: var(--danger); border: 2px solid var(--danger); border-radius: 50%; padding: 2px 6px; display: inline-block; font-weight: bold; }
        .kpi-cards { display: flex; flex-direction: row; justify-content: space-between; gap: 20px; margin: 30px 0; }
        .kpi-card { background: #fff; border-radius: 8px; padding: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.05); border-top: 4px solid var(--primary); flex: 1 1 0; min-width: 250px; max-width: 32%; box-sizing: border-box; }
            .kpi-cards {
                display: flex !important;
                flex-direction: row !important;
                justify-content: space-between !important;
                align-items: stretch !important;
                gap: 20px !important;
                page-break-inside: avoid !important;
            }
            .kpi-card {
                max-width: 32% !important;
                min-width: 250px !important;
                box-sizing: border-box !important;
                page-break-inside: avoid !important;
            }
        .kpi-card h3 { margin-top: 0; color: var(--primary); }
        .kpi-value { font-size: 2em; font-weight: bold; margin: 10px 0; }
        .kpi-change { font-size: 0.9em; }
        .trend-up::before { content: '▲ '; }
        .trend-down::before { content: '▼ '; }
        
        @media (max-width: 768px) {
            .chart-container { flex-direction: column; }
            .chart-box { min-width: 100%; max-width: 100vw; padding: 10px; }
            .kpi-cards { grid-template-columns: 1fr; }
            table, th, td { font-size: 0.92em !important; padding: 6px 4px !important; }
            th, td { white-space: normal !important; }
            .chart-wrapper { max-width: 100vw !important; min-width: 0 !important; height: 260px !important; }
            #mainChart, #chart_sales_compare, #chart_ratio_trend { max-width: 100vw !important; min-width: 0 !important; height: 260px !important; }
        }        
    </style>
</head>
<body>
    <h1 style="font-size:2.2em;letter-spacing:2px;">渠道调拨与任务执行综合报告</h1>
    <div class="container">
        <h2 style="font-size:1.3em;color:#3498db;border-left:5px solid #3498db;padding-left:10px;margin-top:20px;margin-bottom:10px;">年度关键指标卡片</h2>
        <!-- 关键指标卡片区块 -->
        <div class="kpi-cards">
            <div class="kpi-card" style="min-width:250px;max-width:100%;">
                <h3>2023年</h3>
                <div class="kpi-value" style="font-size:1.08em;text-align:left;line-height:1.7;">
                    <div style="background:#e3f2fd;padding:4px 8px;border-radius:4px;font-weight:bold;">销售金额：<span style="color:#3498db;">1.51</span> (亿元)</div>
                    <div style="background:#eaf7ea;padding:4px 8px;border-radius:4px;font-weight:bold;">销售利润：<span style="color:#27ae60;">2652</span> (万元)</div>
                    <div style="background:#f8f9fa;padding:6px 10px 2px 10px;border-radius:4px;margin-top:6px;">
                        <div style="font-size:0.98em;line-height:1.6;">调拨执行店数：<span style="color:#9b59b6;font-weight:bold;">239</span></div>
                        <div style="font-size:0.98em;line-height:1.6;">调拨金额：<span style="color:#e67e22;font-weight:bold;">1500</span> (万元)</div>
                        <div style="font-size:0.98em;line-height:1.6;">调拨占销售比：<span style="color:#e74c3c;font-weight:bold;">9.9%</span></div>
                    </div>
                </div>
            </div>
            <div class="kpi-card" style="min-width:250px;max-width:100%;">
                <h3>2024年</h3>
                <div class="kpi-value" style="font-size:1.08em;text-align:left;line-height:1.7;">
                    <div style="background:#e3f2fd;padding:4px 8px;border-radius:4px;font-weight:bold;">销售金额：<span style="color:#3498db;">1.47</span> (亿元)</div>
                    <div style="background:#eaf7ea;padding:4px 8px;border-radius:4px;font-weight:bold;">销售利润：<span style="color:#27ae60;">2605</span> (万元)</div>
                    <div style="background:#f8f9fa;padding:6px 10px 2px 10px;border-radius:4px;margin-top:6px;">
                        <div style="font-size:0.98em;line-height:1.6;">调拨执行店数：<span style="color:#9b59b6;font-weight:bold;">240</span></div>
                        <div style="font-size:0.98em;line-height:1.6;">调拨金额：<span style="color:#e67e22;font-weight:bold;">1440</span> (万元)</div>
                        <div style="font-size:0.98em;line-height:1.6;">调拨占销售比：<span style="color:#e74c3c;font-weight:bold;">9.7%</span></div>
                    </div>
                </div>
            </div>
            <div class="kpi-card" style="min-width:250px;max-width:100%;">
                <h3>2025年(1~6月)</h3>
                <div class="kpi-value" style="font-size:1.08em;text-align:left;line-height:1.7;">
                    <div style="background:#e3f2fd;padding:4px 8px;border-radius:4px;font-weight:bold;">销售金额：<span style="color:#3498db;">0.66</span> (亿元)</div>
                    <div style="background:#eaf7ea;padding:4px 8px;border-radius:4px;font-weight:bold;">销售利润：<span style="color:#27ae60;">1147</span> (万元)</div>
                    <div style="background:#f8f9fa;padding:6px 10px 2px 10px;border-radius:4px;margin-top:6px;">
                        <div style="font-size:0.98em;line-height:1.6;">调拨执行店数：<span style="color:#9b59b6;font-weight:bold;">249</span></div>
                        <div style="font-size:0.98em;line-height:1.6;">调拨金额：<span style="color:#e67e22;font-weight:bold;">698</span> (万元)</div>
                        <div style="font-size:0.98em;line-height:1.6;">调拨占销售比：<span style="color:#e74c3c;font-weight:bold;">10.5%</span></div>
                    </div>
                </div>
            </div>
        </div>
        <h2 style="font-size:1.2em;">年度调拨任务执行分布</h2>
        <div class="chart-container">
            <div class="chart-box" style="max-width:850px;margin:0 auto;">
                <div class="chart-wrapper" id="taskTrendChart"></div>
            </div>
        </div>

        <br>
        <br>

        <div class="data-section" style="border-left:4px solid #ffa94d;">
            <h2>数据分析摘要</h2>
            <ul>
                <li>2023年至2024年，任务执行率都在50%以下，将近54.3%左右的调拨任务未执行【每一对调拨即作为一项任务计算】</li>
                <li>2023年至2024年总体调拨任务量没多大变化，低执行率的问题一直没有改善。2025年上半年，烟草投放量较往年大，执行率达到了52.9%</li>
                <li>调拨任务量取决于店铺数量及各店香烟的动销趋势和库存情况</li>
                <li>任务总量代表店铺需求指标，执行率体现总部对香烟的重视程度及对店铺的管理能力</li>
        </div>
            </ul>
        </div>
        
        <!-- 渠道调拨数据分析板块 -->
        <h2 id="section1" style="font-size:1.5em;">年度渠道间调拨情况对比</h2>
        <div class="table-responsive"><table>
            <thead>
                <tr><th>年度</th><th>项目\渠道</th><th>渠道一</th><th>渠道二</th><th>渠道一/渠道二</th></tr>
            </thead>
            <tbody>
                <tr><td rowspan="3">2023</td><td>执行店数</td><td>≈239</td><td>≈100</td><td>≈2.4</td></tr>
                <tr><td>调拨金额(万元)</td><td>≈1500</td><td>≈1100</td><td>-</td></tr>
                <tr><td>调拨占比</td><td>≈10%</td><td>≈25%</td><td><span class="circle-highlight">≈0.4</span></td></tr>
                <tr><td rowspan="3">2024</td><td>执行店数</td><td>≈240</td><td>≈103</td><td>≈2.3</td></tr>
                <tr><td>调拨金额(万元)</td><td>≈1440</td><td>≈1006</td><td>-</td></tr>
                <tr><td>调拨占销售比</td><td>≈10%</td><td>≈22%</td><td><span class="circle-highlight">≈0.5</span></td></tr>
                <tr><td rowspan="3">2025</td><td>执行店数</td><td>≈249</td><td>≈105</td><td>≈2.3</td></tr>
                <tr><td>调拨金额(万元)</td><td>≈698</td><td>≈362</td><td>-</td></tr>
                <tr><td>调拨占销售比</td><td>≈10%</td><td>≈28%</td><td><span class="circle-highlight">≈0.3</span></td></tr>
            </tbody>
        </table>
        </div>

        <br>
        <br>

        <h2 style="font-size:1.2em;">渠道调拨数据同比分析</h2>
        <div class="table-responsive"><table>
        </div>
            <thead>
                <tr><th>渠道</th><th>指标</th><th>2024vs2023</th><th>2025vs2024</th></tr>
            </thead>
            <tbody>
                <tr><td rowspan="2">渠道一</td><td>执行店数同比增长</td><td class="trend-up">≈+0.4%</td><td class="trend-up">≈+2.5%</td></tr>
                <tr><td>调拨占比变化</td><td>≈0.0%</td><td class="trend-up">≈+2.0%</td></tr>
                <tr><td rowspan="2">渠道二</td><td>执行店数同比增长</td><td class="trend-up">≈+3.0%</td><td class="trend-up">≈+1.9%</td></tr>
                <tr><td>调拨占比变化</td><td class="trend-down">≈-3.0%</td><td class="trend-up">≈+6.0%</td></tr>
            </tbody>
        </table>

        <br>
        <br>

        <h2 style="font-size:1.2em;" data-short-line>年度数据对比 - 执行店数、调拨金额、调拨占比</h2>
        <div class="chart-container" style="justify-content:flex-start;">
            <div class="chart-box" style="max-width:900px;margin-left:0;">
                <div class="chart-wrapper" id="mainChart" style="max-width:900px;"></div>
            </div>
        </div>

        <br>
        <br>

        <div class="analysis-text" style="border-left:4px solid #3498db;">
            <h2>数据分析摘要</h2>
            <p><strong>1. 执行店数稳步增长：</strong></p>
            <ul>
                <li>渠道一规模更大，从239家增至249家，展现稳健扩张态势</li>
                <li>渠道二店数持续增长，从2023年的100家增至2025年的105家，年均增长率2.5%</li>
                <li>渠道二的店数规模约为渠道一的42%左右，比例保持稳定</li>
            </ul>
            <p><strong>2. 调拨占销售比分析：</strong></p>
            <ul>
                <li>渠道一维持相对稳定（10%-12%），显示标准化的运营模式</li>
                <li>渠道二调拨占比波动较大（22%-28%），表明更灵活的调拨策略</li>
                <li>渠道二的调拨占比始终高于渠道一，平均是渠道一的2.3倍</li>
            </ul>
        </div>
        
        <div class="chart-container">
        </div>
        
        <div class="chart-container">
        <!-- 已删除调拨占销售比趋势对比图表 -->
        </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof echarts === 'undefined') {
                alert('ECharts加载失败，请检查网络连接');
                return;
            }
            // 初始化图表容器
            const mainChart = echarts.init(document.getElementById('mainChart'));
            const taskTrendChart = echarts.init(document.getElementById('taskTrendChart'));
            // 数据准备
            const years = ['2023', '2024', '2025'];
            const seriesData = {
                渠道二: { 执行店数: [100, 103, 105], 调拨金额: [1100, 1006, 362], 调拨占比: [25, 22, 28] },
                渠道一: { 执行店数: [239, 240, 246], 调拨金额: [1500, 1440, 492], 调拨占比: [10, 10, 12] }
            };
            const taskData = {
                总任务数: [906, 869, 444],
                已执行: [406, 403, 235],
                未执行: [500, 466, 209]
            };

            // 严格判断调拨金额和调拨占比是否有有效数据（非null/undefined/空/0）
            function hasValid(arr) {
                return Array.isArray(arr) && arr.some(v => v !== null && v !== undefined && v !== '' && v !== 0);
            }
            const hasAmount = hasValid(seriesData.渠道一.调拨金额) || hasValid(seriesData.渠道二.调拨金额);
            const hasRatio = hasValid(seriesData.渠道一.调拨占比) || hasValid(seriesData.渠道二.调拨占比);
            // 任务执行趋势图配置
            const taskTrendOption = {
                title: { text: '任务执行趋势分析', left: 'center' },
                tooltip: { trigger: 'axis' },
                legend: { data: ['总任务数', '已执行', '未执行'], bottom: 10 },
                grid: { left: '3%', right: '4%', bottom: '15%', top: '15%', containLabel: true },
                xAxis: { type: 'category', data: years },
                yAxis: { type: 'value' },
                series: [
                    { name: '总任务数', type: 'line', data: taskData.总任务数 },
                    {
                        name: '已执行',
                        type: 'bar',
                        data: taskData.已执行,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                // 百分比=已执行/总任务数
                                const idx = params.dataIndex;
                                const percent = taskData.总任务数[idx] ? (params.value / taskData.总任务数[idx] * 100) : 0;
                                return percent.toFixed(1) + '%';
                            },
                            color: '#27ae60',
                            fontWeight: 'bold',
                            fontSize: 13
                        }
                    },
                    {
                        name: '未执行',
                        type: 'bar',
                        data: taskData.未执行,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                // 百分比=未执行/总任务数
                                const idx = params.dataIndex;
                                const percent = taskData.总任务数[idx] ? (params.value / taskData.总任务数[idx] * 100) : 0;
                                return percent.toFixed(1) + '%';
                            },
                            color: '#e74c3c',
                            fontWeight: 'bold',
                            fontSize: 13
                        }
                    }
                ]
            };
            // 主图表配置
            const mainOption = {
                // 去除标题
                tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
                legend: {
                    data: [
                        '渠道一-执行店数', '渠道二-执行店数',
                        '渠道一-调拨金额', '渠道二-调拨金额',
                        '渠道一-调拨占比', '渠道二-调拨占比'
                    ],
                    bottom: 10,
                    itemWidth: 22,
                    itemHeight: 14,
                    textStyle: {
                        fontWeight: 'bold',
                        fontSize: 13,
                        color: '#2c3e50',
                        fontFamily: 'Microsoft YaHei, sans-serif'
                    },
                    selected: {
                        '渠道一-执行店数': true,
                        '渠道二-执行店数': true,
                        '渠道一-调拨金额': true,
                        '渠道二-调拨金额': true,
                        '渠道一-调拨占比': true,
                        '渠道二-调拨占比': true
                    }
                },
                grid: { left: '3%', right: '4%', bottom: '15%', top: '15%', containLabel: true },
                xAxis: {
                    type: 'category',
                    data: years,
                    axisLabel: {
                        fontWeight: 'bold',
                        fontSize: 16,
                        color: '#2c3e50',
                        fontFamily: 'Microsoft YaHei, sans-serif'
                    },
                    nameTextStyle: {
                        fontWeight: 'bold',
                        fontSize: 16,
                        color: '#3498db',
                        fontFamily: 'Microsoft YaHei, sans-serif'
                    }
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '执行店数',
                        axisLabel: {
                            fontWeight: 'bold',
                            fontSize: 13,
                            color: '#2c3e50',
                            fontFamily: 'Microsoft YaHei, sans-serif',
                            formatter: '{value}家'
                        },
                        nameTextStyle: {
                            fontWeight: 'bold',
                            fontSize: 13,
                            color: '#3498db',
                            fontFamily: 'Microsoft YaHei, sans-serif'
                        }
                    },
                    {
                        type: 'value',
                        name: hasAmount ? '调拨金额' : '',
                        position: 'right',
                        offset: 0,
                        axisLabel: hasAmount ? { fontWeight: 'bold', fontSize: 13, formatter: '{value}万元', margin: 22 } : { show: false },
                        nameTextStyle: hasAmount ? { fontWeight: 'bold', fontSize: 13, padding: [0, 0, 0, 16] } : {},
                        splitLine: { show: false }
                    },
                    {
                        type: 'value',
                        name: hasRatio ? '调拨占比(%)' : '',
                        position: 'right',
                        offset: 110,
                        axisLabel: hasRatio ? { formatter: '{value}%', fontWeight: 'bold', fontSize: 13, margin: 22 } : { show: false },
                        nameTextStyle: hasRatio ? { fontWeight: 'bold', fontSize: 13, padding: [0, 0, 0, 16] } : {},
                        splitLine: { show: false }
                    }
                ],
                series: [
                    {
                        name: '渠道一-执行店数',
                        type: 'bar',
                        data: seriesData.渠道一.执行店数,
                        barWidth: 16,
                        barCategoryGap: '10%',
                        itemStyle: { color: '#27ae60' }, // 浅绿，与任务执行趋势一致
                        label: {
                            show: false
                        },
                        yAxisIndex: 0
                    },
                    {
                        name: '渠道二-执行店数',
                        type: 'bar',
                        data: seriesData.渠道二.执行店数,
                        barWidth: 16,
                        barCategoryGap: '10%',
                        itemStyle: { color: '#3498db' }, // 蓝色
                        label: {
                            show: false
                        },
                        yAxisIndex: 0
                    },
                    {
                        name: '渠道一-调拨金额',
                        type: 'bar',
                        data: seriesData.渠道一.调拨金额,
                        barWidth: 18,
                        barCategoryGap: '10%',
                        itemStyle: { color: '#f39c12' }, // 橙色，与任务执行趋势一致
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}万',
                            fontWeight: 'bold',
                            fontSize: 13,
                            color: '#f39c12', // 橙色，与任务执行趋势一致
                            fontFamily: 'Microsoft YaHei, sans-serif'
                        },
                        yAxisIndex: 1
                    },
                    {
                        name: '渠道二-调拨金额',
                        type: 'bar',
                        data: seriesData.渠道二.调拨金额,
                        barWidth: 18,
                        barCategoryGap: '10%',
                        itemStyle: { color: '#fff', borderColor: '#3498db', borderWidth: 2 }, // 白色+蓝色描边
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}万',
                            fontWeight: 'bold',
                            fontSize: 13,
                            color: '#3498db',
                            fontFamily: 'Microsoft YaHei, sans-serif'
                        },
                        yAxisIndex: 1
                    },
                    {
                        name: '渠道一-调拨占比',
                        type: 'line',
                        yAxisIndex: 2,
                        data: seriesData.渠道一.调拨占比,
                        symbol: 'circle',
                        symbolSize: 12,
                        lineStyle: { color: '#27ae60', width: 4 },
                        itemStyle: { color: '#27ae60' },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%',
                            fontWeight: 'bold',
                            fontSize: 13,
                            color: '#27ae60',
                            fontFamily: 'Microsoft YaHei, sans-serif'
                        }
                    },
                    {
                        name: '渠道二-调拨占比',
                        type: 'line',
                        yAxisIndex: 2,
                        data: seriesData.渠道二.调拨占比,
                        symbol: 'circle',
                        symbolSize: 12,
                        lineStyle: { color: '#3498db', width: 4 },
                        itemStyle: { color: '#fff', borderColor: '#3498db', borderWidth: 2 },
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%',
                            fontWeight: 'bold',
                            fontSize: 13,
                            color: '#3498db',
                            fontFamily: 'Microsoft YaHei, sans-serif'
                        }
                    }
                ]
            };
            // 渲染图表
            taskTrendChart.setOption(taskTrendOption);
            mainChart.setOption(mainOption);
            // 响应式调整
            window.addEventListener('resize', function() {
                taskTrendChart.resize();
                mainChart.resize();
            });

            // PDF打印优化 - 确保图表在打印前正确渲染
            window.addEventListener('beforeprint', function() {
                // 强制重新渲染所有ECharts图表
                setTimeout(function() {
                    taskTrendChart.resize();
                    mainChart.resize();

                    // 如果存在其他ECharts实例，也进行resize
                    if (window.myChart) window.myChart.resize();
                    if (window.myChart2) window.myChart2.resize();
                }, 100);
            });

            // 打印后恢复
            window.addEventListener('afterprint', function() {
                setTimeout(function() {
                    taskTrendChart.resize();
                    mainChart.resize();
                    if (window.myChart) window.myChart.resize();
                    if (window.myChart2) window.myChart2.resize();
                }, 100);
            });
        });
    </script>
</body>
    <!-- 商品店间调拨工作重要性总结报告（图文版）板块开始 -->
    <hr style="margin:40px 0;">
    <section id="appendix-report">
        <h2>2024年渠道销售与调拨月数据总览</h2>
        <div class="table-responsive"><table border="1" style="border-collapse:collapse;text-align:center;width:100%;">
        </div>
          <tr style="background:#cce6f7;">
            <th rowspan="2">渠道</th>
            <th rowspan="2">月份</th>
            <th colspan="12">1月-12月</th>
            <th rowspan="2">合计金额</th>
          </tr>
          <tr style="background:#cce6f7;">
            <td>1月</td><td>2月</td><td>3月</td><td>4月</td><td>5月</td><td>6月</td>
            <td>7月</td><td>8月</td><td>9月</td><td>10月</td><td>11月</td><td>12月</td>
          </tr>
          <tr>
            <td rowspan="3">渠道一</td>
            <td>销售金额(万)</td>
            <td>1100.67</td><td>1011.69</td><td>1245.33</td><td>1306.31</td><td>1378.35</td><td>1362.86</td><td>1397.89</td><td>1365.53</td><td>1310.86</td><td>1177.42</td><td>1096.42</td><td>981.47</td><td>14734.78</td>
          </tr>
          <tr>
            <td>调拨金额(万)</td>
            <td>125.02</td><td>97.46</td><td>84.33</td><td>128.79</td><td>124.54</td><td>126.12</td><td>108.46</td><td>175.42</td><td>135.43</td><td>121.90</td><td>112.23</td><td>100.56</td><td>1440.26</td>
          </tr>
          <tr>
            <td>调拨占比</td>
            <td>11%</td><td>10%</td><td>7%</td><td>10%</td><td>9%</td><td>9%</td><td>8%</td><td>13%</td><td>10%</td><td>10%</td><td>10%</td><td>10%</td><td>10%</td>
          </tr>
          <tr>
            <td rowspan="3">渠道二</td>
            <td>销售金额(万)</td>
            <td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td>
          </tr>
          <tr>
            <td>调拨金额(万)</td>
            <td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td><td>***</td>
          </tr>
          <tr>
            <td>调拨占比</td>
            <td>20%</td><td>23%</td><td>17%</td><td>20%</td><td>20%</td><td>24%</td><td>24%</td><td>24%</td><td>24%</td><td>22%</td><td>19%</td><td>23%</td><td>22%</td>
          </tr>
        </table>

        <h2>2024年数据可视化分析</h2>
        <h3>图1： 渠道一与渠道二销售与调拨对比</h3>
        <div id="chart_sales_compare" style="width:100%;max-width:1000px;height:340px;margin:0 auto 30px auto;"></div>
        <script src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
        <script>
        // 填充完整数据
        var salesData1 = [1100.67, 1011.69, 1245.33, 1306.31, 1378.35, 1362.86, 1397.89, 1365.53, 1310.86, 1177.42216, 1096.41545, 981.4676];
        var transferData1 = [125.02, 97.46, 84.33, 128.79, 124.54, 126.12, 108.46, 175.42, 135.43, 121.90349, 112.23466, 100.56251];
        var salesData2 = [386.91, 285.12, 387.94, 404.88, 402.27, 403.77, 441.50, 433.12, 425.63, 374.16, 370.28, 337.08];
        var transferData2 = [77.10, 64.35, 67.28, 80.22, 78.44, 97.69, 104.64, 104.13, 100.93, 81.28, 71.96, 78.30];
        var months = ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];
        var chartDom = document.getElementById('chart_sales_compare');
        var myChart = echarts.init(chartDom, null, {
            width: 'auto',
            height: 340,
            renderer: 'canvas'
        });
        // 将图表实例保存到全局变量以便打印时调用
        window.myChart = myChart;
var option = {
    title: { text: '渠道一/二销售与调拨金额月度对比', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { data: ['渠道二销售','渠道二调拨','渠道一销售','渠道一调拨'], top: 30 },
    xAxis: { type: 'category', data: months },
    yAxis: [
        {
            type: 'value',
            name: '渠道一金额(万)',
            position: 'left',
            axisLine: { show: true, lineStyle: { color: '#27ae60' } },
            axisLabel: { color: '#27ae60', fontWeight: 'bold' }
        },
        {
            type: 'value',
            name: '渠道二金额(万)',
            position: 'right',
            axisLine: { show: true, lineStyle: { color: '#3498db' } },
            axisLabel: { color: '#3498db', fontWeight: 'bold' }
        }
    ],
    series: [
        { name: '渠道一销售', type: 'bar', data: salesData1, itemStyle: { color: '#27ae60' }, stack: '渠道一', yAxisIndex: 0 },
        { name: '渠道一调拨', type: 'bar', data: transferData1, itemStyle: { color: '#e74c3c' }, stack: '渠道一', yAxisIndex: 0 },
        { name: '渠道二销售', type: 'bar', data: salesData2, stack: '渠道二', yAxisIndex: 1 },
        { name: '渠道二调拨', type: 'bar', data: transferData2, itemStyle: { color: '#fff', borderColor: '#3498db', borderWidth: 2 }, stack: '渠道二', yAxisIndex: 1 }
    ]
};
        myChart.setOption(option);

        // 添加窗口resize事件监听
        window.addEventListener('resize', function() {
            myChart.resize();
        });
        </script>
        <p>如上图所示，渠道一店铺数量多、销售总量大，但全年调拨销售占比仅为10%，远低于渠道二的22%。渠道二虽然体量较小，但调拨执行效率高，调拨销售占比高，调拨工作对销售促进作用明显。通过对比可以看出，渠道一的调拨工作还有很大提升空间，只有进一步加强店间调拨，才能争取更多的额外销售贡献。</p>

        <!-- 图2、图3（Chart.js）内容同步自“再次修改.html”，并排展示，标题与图表同一段落 -->
        <div class="two-columns" style="display:flex;gap:12px;page-break-inside:avoid;">
            <div class="column" style="flex:0 0 48%;min-width:0;">
                <h3>图2：渠道一销售金额与调拨金额月度对比</h3>
                <div class="chart-container" style="background-color:white;padding:10px;border-radius:5px;box-shadow:0 2px 10px rgba(0,0,0,0.1);margin-bottom:0px;height:420px;">
                    <div class="chart" style="height:320px;width:100%;max-width:100%;margin:0 auto 8px auto;">
                        <canvas id="channel1SalesTransferChart"></canvas>
                    </div>
                    <div class="chart-description" style="text-align:left;font-size:0.82em;line-height:1.5;padding:8px;background-color:#f8f9fa;border-radius:3px;border:1px solid #ddd;margin:0;width:calc(100% - 18px);box-sizing:border-box;">
                        <div style="margin-bottom:4px;"><span style="font-weight:bold;color:#2c3e50;">销售旺季（3-5月，8-10月）</span>调拨金额明显提升，调拨补货有效满足市场需求，促进销售增长。</div>
                        <div><span style="color:#3498db;font-weight:bold;">8月销售金额：</span>1365.53万元，<span style="color:#e67e22;font-weight:bold;">调拨金额：</span>175.42万元，<span style="color:#e74c3c;font-weight:bold;">调拨占比：</span>12.8%。</div>
                    </div>
                </div>
            </div>

            <div class="column" style="flex:0 0 52%;min-width:0;margin-left:-8px;">
                <h3>图3：渠道二销售金额与调拨金额月度对比</h3>
                <div class="chart-container" style="background-color:white;padding:8px;border-radius:5px;box-shadow:0 2px 10px rgba(0,0,0,0.1);margin-bottom:0px;height:420px;">
                    <div class="chart" style="height:320px;width:100%;max-width:100%;margin:0 auto 8px auto;">
                        <canvas id="channel2SalesTransferChart"></canvas>
                    </div>
                    <div class="chart-description" style="text-align:left;font-size:0.82em;line-height:1.5;padding:6px;background-color:#f8f9fa;border-radius:3px;border:1px solid #ddd;margin:0;width:calc(100% - 14px);box-sizing:border-box;">
                        <div style="margin-bottom:4px;"><span style="font-weight:bold;color:#2c3e50;">渠道二调拨占比显著高于渠道一，平均达到</span><span style="color:#e74c3c;font-weight:bold;">21.4%</span>。<span style="color:#3498db;font-weight:bold;">6月、8月、12月调拨占比：</span>均超过<span style="color:#e74c3c;font-weight:bold;">24%</span>。</div>
                        <div><span style="color:#2c3e50;">在同业态渠道中，各店库存标准方差接近，调拨金额占比越高，对销售贡献越大。</span><span style="color:#27ae60;font-weight:bold;">渠道二高调拨占比</span>，体现了其调拨执行力强，对销售增长促进作用明显。</div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            Chart.register(ChartDataLabels);

            // Chart.js全局配置优化，适配打印
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;
            // 图2：渠道一
            const channel1SalesTransferCtx = document.getElementById('channel1SalesTransferChart').getContext('2d');
            const channel1SalesData = [1100.67, 1011.69, 1245.33, 1306.31, 1378.35, 1362.86, 1397.89, 1365.53, 1310.86, 1177.42, 1096.42, 981.47];
            const channel1TransferData = [125.02, 97.46, 84.33, 128.79, 124.54, 126.12, 108.46, 175.42, 135.43, 121.90, 112.23, 100.56];
            const channel1BaseSalesData = channel1SalesData.map((sales, i) => sales - channel1TransferData[i]);
            const channel1TransferPercent = channel1SalesData.map((sales, i) => (channel1TransferData[i] / sales * 100).toFixed(1) + '%');
            new Chart(channel1SalesTransferCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [
                        {
                            label: '基础销售金额(万)',
                            data: channel1BaseSalesData,
                            backgroundColor: 'rgba(54, 162, 235, 0.6)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1,
                            fill: true,
                            stack: '总量',
                            tension: 0.1
                        },
                        {
                            label: '调拨金额(万)',
                            data: channel1TransferData,
                            backgroundColor: 'rgba(255, 159, 64, 0.7)',
                            borderColor: 'rgba(255, 159, 64, 1)',
                            borderWidth: 1,
                            fill: true,
                            stack: '总量',
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    devicePixelRatio: 2, // 提高打印质量
                    layout: {
                        padding: {
                            left: 5,
                            right: 5,
                            top: 5,
                            bottom: 15
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额(万元)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                autoSkip: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '渠道一销售金额与调拨金额月度对比'
                        },
                        tooltip: {
                            callbacks: {
                                beforeLabel: function(context) {
                                    return `${context.label}月`;
                                },
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw.toFixed(2)}万元`;
                                },
                                afterLabel: function(context) {
                                    if(context.datasetIndex === 1) {
                                        const total = context.chart.data.datasets[0].data[context.dataIndex] + context.raw;
                                        return `总销售: ${total.toFixed(2)}万元\n调拨占比: ${(context.raw/total*100).toFixed(1)}%`;
                                    }
                                    return null;
                                }
                            }
                        },
                        datalabels: {
                            display: true,
                            color: '#333',
                            font: {
                                size: 11,
                                weight: 'bold'
                            },
                            formatter: function(value, context) {
                                if(context.datasetIndex === 1) {
                                    return channel1TransferPercent[context.dataIndex];
                                }
                                return null;
                            },
                            anchor: 'center',
                            align: 'center',
                            clamp: true,
                            offset: function(context) {
                                const chart = context.chart;
                                const meta = chart.getDatasetMeta(context.datasetIndex);
                                const yScale = chart.scales.y;
                                const base = channel1BaseSalesData[context.dataIndex];
                                const transfer = channel1TransferData[context.dataIndex];
                                const midPoint = base + (transfer / 2);
                                const pixel = yScale.getPixelForValue(midPoint);
                                return pixel - context.chart.chartArea.bottom;
                            }
                        }
                    }
                },
                plugins: [ChartDataLabels]
            });
            // 图3：渠道二
            const channel2SalesTransferCtx = document.getElementById('channel2SalesTransferChart').getContext('2d');
            const channel2SalesData = [386.91, 285.12, 387.94, 404.88, 402.27, 403.77, 441.50, 433.12, 425.63, 374.16, 370.28, 337.08];
            const channel2TransferData = [77.10, 64.35, 67.28, 80.22, 78.44, 97.69, 104.64, 104.13, 100.93, 81.28, 71.96, 78.30];
            const channel2BaseSalesData = channel2SalesData.map((sales, i) => sales - channel2TransferData[i]);
            const channel2TransferPercent = channel2SalesData.map((sales, i) => (channel2TransferData[i] / sales * 100).toFixed(1) + '%');
            new Chart(channel2SalesTransferCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [
                        {
                            label: '基础销售金额(万)',
                            data: channel2BaseSalesData,
                            backgroundColor: 'rgba(75, 192, 192, 0.6)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1,
                            fill: true,
                            stack: '总量',
                            tension: 0.1
                        },
                        {
                            label: '调拨金额(万)',
                            data: channel2TransferData,
                            backgroundColor: 'rgba(153, 102, 255, 0.7)',
                            borderColor: 'rgba(153, 102, 255, 1)',
                            borderWidth: 1,
                            fill: true,
                            stack: '总量',
                            tension: 0.1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    devicePixelRatio: 2, // 提高打印质量
                    layout: {
                        padding: {
                            left: 5,
                            right: 5,
                            top: 5,
                            bottom: 15
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '金额(万元)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                autoSkip: false
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '渠道二销售金额与调拨金额月度对比'
                        },
                        tooltip: {
                            callbacks: {
                                beforeLabel: function(context) {
                                    return `${context.label}月`;
                                },
                                label: function(context) {
                                    return `${context.dataset.label}: ${context.raw.toFixed(2)}万元`;
                                },
                                afterLabel: function(context) {
                                    if(context.datasetIndex === 1) {
                                        const total = context.chart.data.datasets[0].data[context.dataIndex] + context.raw;
                                        return `总销售: ${total.toFixed(2)}万元\n调拨占比: ${(context.raw/total*100).toFixed(1)}%`;
                                    }
                                    return null;
                                }
                            }
                        },
                        datalabels: {
                            display: true,
                            color: '#333',
                            font: {
                                size: 11,
                                weight: 'bold'
                            },
                            formatter: function(value, context) {
                                if(context.datasetIndex === 1) {
                                    return channel2TransferPercent[context.dataIndex];
                                }
                                return null;
                            },
                            anchor: 'center',
                            align: 'center',
                            clamp: true,
                            offset: function(context) {
                                const chart = context.chart;
                                const meta = chart.getDatasetMeta(context.datasetIndex);
                                const yScale = chart.scales.y;
                                const base = channel2BaseSalesData[context.dataIndex];
                                const transfer = channel2TransferData[context.dataIndex];
                                const midPoint = base + (transfer / 2);
                                const pixel = yScale.getPixelForValue(midPoint);
                                return pixel - context.chart.chartArea.bottom;
                            }
                        }
                    }
                },
                plugins: [ChartDataLabels]
            });
        });

        // 通用打印优化函数
        function optimizeChartsForPrint() {
            // 强制所有Chart.js图表重新渲染
            Chart.instances.forEach(function(chart) {
                if (chart && typeof chart.resize === 'function') {
                    chart.resize();
                }
            });

            // 强制所有ECharts图表重新渲染
            if (window.echarts) {
                const charts = [window.myChart, window.myChart2, taskTrendChart, mainChart];
                charts.forEach(function(chart) {
                    if (chart && typeof chart.resize === 'function') {
                        chart.resize();
                    }
                });
            }
        }

        // 打印前优化
        window.addEventListener('beforeprint', function() {
            setTimeout(optimizeChartsForPrint, 200);
        });

        // 打印后恢复
        window.addEventListener('afterprint', function() {
            setTimeout(optimizeChartsForPrint, 200);
        });

        // 页面加载完成后的最终优化
        window.addEventListener('load', function() {
            setTimeout(optimizeChartsForPrint, 500);
        });
        </script>

        <br>
        <br>

        <h3>2. 月度趋势分析</h3>
        <div id="chart_ratio_trend" style="width:100%;max-width:1000px;height:340px;margin:0 auto 30px auto;"></div>
        <script>
        // 调拨占比数据
        var ratio1 = [0.11, 0.10, 0.07, 0.10, 0.09, 0.09, 0.08, 0.13, 0.10, 0.10, 0.10, 0.10];
        var ratio2 = [0.20, 0.23, 0.17, 0.20, 0.20, 0.24, 0.24, 0.24, 0.24, 0.22, 0.19, 0.23];
        var chartDom2 = document.getElementById('chart_ratio_trend');
        var myChart2 = echarts.init(chartDom2, null, {
            width: 'auto',
            height: 340,
            renderer: 'canvas'
        });
        // 将图表实例保存到全局变量以便打印时调用
        window.myChart2 = myChart2;
        var option2 = {
            title: { text: '渠道一/二调拨占比月度趋势', left: 'center' },
            tooltip: { trigger: 'axis' },
            legend: { data: ['渠道一调拨占比','渠道二调拨占比'], top: 30 },
            xAxis: { type: 'category', data: months },
            yAxis: { type: 'value', name: '调拨占比', min: 0, max: 0.3 },
            series: [
                { name: '渠道一调拨占比', type: 'line', data: ratio1, lineStyle: { color: '#27ae60' }, itemStyle: { color: '#27ae60' } },
                { name: '渠道二调拨占比', type: 'line', data: ratio2, lineStyle: { color: '#3498db' }, itemStyle: { color: '#3498db' } }
            ]
        };
        myChart2.setOption(option2);

        // 添加窗口resize事件监听
        window.addEventListener('resize', function() {
            myChart2.resize();
        });
        </script>
        <p>调拨占比趋势图进一步直观显示，渠道一的调拨占比始终低于渠道二，且全年平均仅为10%，而渠道二高达22%。渠道二的调拨工作成效显著，带动了更高的调拨销售占比。渠道一若能提升调拨效率和执行力，将有望获得更多的销售增长空间。</p>

        <h3>3. 合计数据分析</h3>
        <p>全年合计数据显示，渠道二调拨销售占比高，调拨效率高，对销售促进作用明显。渠道一调拨销售占比低，调拨工作尚有较大提升空间。建议渠道一借鉴渠道二经验，优化调拨流程，提高调拨执行效率，进一步提升整体销售业绩。</p>

        <h2>三、结论与建议</h2>
        <ol>
          <li><b>调拨工作对销售提升作用明显：</b> 渠道二的高调拨占比带动了销售增长，说明高效的调拨机制能够有效缓解库存结构性矛盾，提升整体业绩。</li>
          <li><b>渠道一提升空间大：</b> 渠道一虽然体量大，但调拨销售占比低，调拨工作尚有较大提升空间。建议加强店间调拨，优化库存配置，提升销售转化率。</li>
          <li><b>经验借鉴与机制优化：</b> 建议渠道一学习渠道二的调拨管理经验，完善调拨流程，提升调拨响应速度和精准度，推动企业整体销售业绩持续增长。</li>
        </ol>

        <h2>四、后续工作建议</h2>
        <ul>
          <li>持续监测各渠道调拨与销售数据，动态调整调拨策略。</li>
          <li>建立调拨绩效考核机制，激励门店积极参与调拨。</li>
          <li>推动信息化系统建设，实现调拨全流程数字化、可视化管理。</li>
        </ul>
    </section>
    <!-- 商品店间调拨工作重要性总结报告（图文版）板块结束 -->
</div>
</body>
</html>